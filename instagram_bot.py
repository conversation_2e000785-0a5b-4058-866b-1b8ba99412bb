"""
인스타그램 자동화 봇 모듈
"""
import os
import time
import random
import logging
import re
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

import config
from gemini_helper import GeminiHelper

# 로깅 설정
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("instagram_bot.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("InstagramBot")

class InstagramBot:
    """인스타그램 자동화 봇 클래스"""

    def __init__(self):
        """초기화 함수"""
        self.username = config.USERNAME
        self.password = config.PASSWORD
        self.base_url = "https://www.instagram.com/"
        self.driver = self._setup_driver()
        self.gemini = GeminiHelper()
        self.logged_in = False
        self.wait = WebDriverWait(self.driver, 10)  # 재사용 가능한 WebDriverWait 객체

    def _setup_driver(self):
        """웹드라이버 설정 - 성능 최적화"""
        chrome_options = Options()

        # 헤드리스 모드 설정
        if config.HEADLESS:
            chrome_options.add_argument("--headless")

        # 성능 최적화 옵션들
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-infobars")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-logging")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--log-level=3")
        chrome_options.add_argument("--silent")
        chrome_options.add_argument("--lang=ko-KR")

        # 메모리 사용량 최적화
        chrome_options.add_argument("--memory-pressure-off")
        chrome_options.add_argument("--max_old_space_size=4096")

        # 사용자 에이전트 설정 (최신 버전)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.implicitly_wait(3)  # 기본 대기 시간 단축
        return driver

    def login(self):
        """인스타그램 로그인 - 안정성 개선 버전"""
        if self.logged_in:
            logger.info("이미 로그인되어 있습니다.")
            return True

        try:
            logger.info("인스타그램 로그인 시도 중...")
            self.driver.get(self.base_url)

            # 페이지 로딩 충분히 대기
            time.sleep(5)

            # 로그인 폼 요소 대기 (여러 방법 시도)
            username_input = None
            login_selectors = [
                (By.NAME, "username"),
                (By.XPATH, "//input[@name='username']"),
                (By.XPATH, "//input[@aria-label='전화번호, 사용자 이름 또는 이메일']"),
                (By.XPATH, "//input[@placeholder='전화번호, 사용자 이름 또는 이메일']")
            ]

            for selector_type, selector_value in login_selectors:
                try:
                    username_input = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((selector_type, selector_value))
                    )
                    break
                except:
                    continue

            if not username_input:
                logger.error("사용자명 입력 필드를 찾을 수 없습니다.")
                return False

            # 쿠키 수락 버튼 클릭 (있는 경우)
            try:
                cookie_selectors = [
                    "//button[contains(text(), '수락') or contains(text(), 'Accept')]",
                    "//button[contains(text(), '모두 허용') or contains(text(), 'Allow all')]",
                    "//button[contains(@class, 'aOOlW') and contains(text(), '수락')]"
                ]

                for selector in cookie_selectors:
                    try:
                        cookie_button = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        cookie_button.click()
                        time.sleep(1)
                        logger.info("쿠키 수락 버튼 클릭 성공")
                        break
                    except:
                        continue
            except:
                logger.info("쿠키 수락 버튼이 없거나 이미 처리되었습니다.")

            # 비밀번호 입력 필드 찾기
            password_input = None
            password_selectors = [
                (By.NAME, "password"),
                (By.XPATH, "//input[@name='password']"),
                (By.XPATH, "//input[@type='password']")
            ]

            for selector_type, selector_value in password_selectors:
                try:
                    password_input = self.driver.find_element(selector_type, selector_value)
                    break
                except:
                    continue

            if not password_input:
                logger.error("비밀번호 입력 필드를 찾을 수 없습니다.")
                return False

            # 로그인 정보 입력
            username_input.clear()
            username_input.send_keys(self.username)
            time.sleep(1)

            password_input.clear()
            password_input.send_keys(self.password)
            time.sleep(1)

            # 로그인 버튼 클릭
            login_selectors = [
                "//button[@type='submit']",
                "//button[contains(text(), '로그인') or contains(text(), 'Log in')]",
                "//div[contains(text(), '로그인') or contains(text(), 'Log in')][@role='button']"
            ]

            login_clicked = False
            for selector in login_selectors:
                try:
                    login_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    login_button.click()
                    login_clicked = True
                    logger.info("로그인 버튼 클릭 성공")
                    break
                except:
                    continue

            if not login_clicked:
                logger.error("로그인 버튼을 찾을 수 없습니다.")
                return False

            # 로그인 성공 확인 (여러 방법 시도)
            try:
                # 홈 페이지 요소들 중 하나라도 나타나면 성공
                success_selectors = [
                    "//a[contains(@href, '/direct/inbox/')]",
                    "//svg[@aria-label='홈' or @aria-label='Home']",
                    "//a[@href='/']",
                    "//nav[@role='navigation']",
                    "//div[contains(@class, 'x1iyjqo2')]"
                ]

                for selector in success_selectors:
                    try:
                        WebDriverWait(self.driver, 15).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                        logger.info("로그인 성공!")
                        self.logged_in = True
                        self._handle_login_popups()
                        return True
                    except:
                        continue

                logger.error("로그인 실패: 성공 확인 요소를 찾을 수 없음")
                return False

            except Exception as e:
                logger.error(f"로그인 성공 확인 중 오류: {e}")
                return False

        except Exception as e:
            logger.error(f"로그인 중 오류 발생: {e}")
            return False

    def _handle_login_popups(self):
        """로그인 후 팝업 처리"""
        # 정보 저장 나중에 하기
        try:
            not_now_button = WebDriverWait(self.driver, 3).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '나중에') or contains(text(), 'Not Now')]"))
            )
            not_now_button.click()
            time.sleep(0.5)
        except:
            logger.info("'나중에 하기' 버튼이 없거나 이미 처리되었습니다.")

        # 알림 설정 나중에 하기
        try:
            not_now_button = WebDriverWait(self.driver, 3).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '나중에') or contains(text(), 'Not Now')]"))
            )
            not_now_button.click()
        except:
            logger.info("'알림 설정' 버튼이 없거나 이미 처리되었습니다.")

    def search_hashtag(self, hashtag):
        """해시태그 검색"""
        if not self.logged_in and not self.login():
            logger.error("로그인이 필요합니다.")
            return False

        try:
            logger.info(f"해시태그 #{hashtag} 검색 중...")
            self.driver.get(f"{self.base_url}explore/tags/{hashtag}/")
            time.sleep(3)  # 페이지 로딩 대기
            return True
        except Exception as e:
            logger.error(f"해시태그 검색 중 오류 발생: {e}")
            return False

    def get_posts(self, count=9):
        """현재 페이지에서 게시물 링크 가져오기 - 개선된 버전"""
        try:
            logger.info(f"게시물 {count}개 가져오는 중...")

            # 페이지 로딩 대기
            time.sleep(3)

            # 게시물 링크 selector들 (최신 Instagram UI 대응)
            post_selectors = [
                "//a[contains(@href, '/p/')]",
                "//a[contains(@href, '/reel/')]",
                "//div[contains(@class, '_aagw')]//a",
                "//article//a[contains(@href, '/p/')]",
                "//div[@role='button']//a[contains(@href, '/p/')]"
            ]

            post_links = []

            for selector in post_selectors:
                try:
                    posts = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_all_elements_located((By.XPATH, selector))
                    )

                    for post in posts:
                        href = post.get_attribute("href")
                        if href and '/p/' in href and href not in post_links:
                            post_links.append(href)

                        if len(post_links) >= count:
                            break

                    if len(post_links) >= count:
                        break

                except:
                    continue

            # 스크롤해서 더 많은 게시물 로드 (필요한 경우)
            if len(post_links) < count:
                logger.info("더 많은 게시물을 로드하기 위해 스크롤합니다...")
                for _ in range(3):  # 최대 3번 스크롤
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(2)

                    # 새로운 게시물 찾기
                    for selector in post_selectors:
                        try:
                            posts = self.driver.find_elements(By.XPATH, selector)
                            for post in posts:
                                href = post.get_attribute("href")
                                if href and '/p/' in href and href not in post_links:
                                    post_links.append(href)

                                if len(post_links) >= count:
                                    break

                            if len(post_links) >= count:
                                break
                        except:
                            continue

                    if len(post_links) >= count:
                        break

            logger.info(f"{len(post_links)}개 게시물 링크 수집 완료")
            return post_links[:count]  # 요청한 개수만큼 반환

        except Exception as e:
            logger.error(f"게시물 가져오기 중 오류 발생: {e}")
            return []

    def like_post(self):
        """현재 게시물 좋아요 - 개선된 버전"""
        try:
            # 최신 Instagram UI에 맞는 좋아요 버튼 selector들
            like_selectors = [
                "//button[.//*[local-name()='svg'][@aria-label='좋아요' or @aria-label='Like']]",
                "//div[@role='button'][.//*[local-name()='svg'][@aria-label='좋아요' or @aria-label='Like']]",
                "//*[local-name()='svg'][@aria-label='좋아요' or @aria-label='Like']/ancestor::button",
                "//*[local-name()='svg'][@aria-label='좋아요' or @aria-label='Like']/parent::*",
                "//span[contains(@class, '_aamw')]//*[local-name()='svg'][@aria-label='좋아요' or @aria-label='Like']"
            ]

            # 이미 좋아요 눌렀는지 확인
            unlike_selectors = [
                "//button[.//*[local-name()='svg'][@aria-label='좋아요 취소' or @aria-label='Unlike']]",
                "//*[local-name()='svg'][@aria-label='좋아요 취소' or @aria-label='Unlike']",
                "//span[contains(@class, '_aamw')]//*[local-name()='svg'][@aria-label='좋아요 취소' or @aria-label='Unlike']"
            ]

            # 이미 좋아요 상태인지 확인
            for selector in unlike_selectors:
                try:
                    self.driver.find_element(By.XPATH, selector)
                    logger.info("이미 좋아요를 누른 게시물입니다.")
                    return False
                except NoSuchElementException:
                    continue

            # 좋아요 버튼 찾기 및 클릭
            for selector in like_selectors:
                try:
                    like_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    like_button.click()
                    logger.info("게시물 좋아요 성공!")
                    time.sleep(random.uniform(0.5, 1.5))  # 자연스러운 지연
                    return True
                except:
                    continue

            logger.error("좋아요 버튼을 찾을 수 없습니다.")
            return False

        except Exception as e:
            logger.error(f"좋아요 중 오류 발생: {e}")
            return False

    def _remove_emojis(self, text):
        """이모지 및 특수 유니코드 문자 제거"""
        # BMP(Basic Multilingual Plane) 외의 문자 제거
        return re.sub(r'[^\u0000-\uFFFF]', '', text)

    def comment_post(self, comment=None):
        """현재 게시물에 댓글 작성 - 개선된 버전"""
        if not comment:
            # 게시물 내용 가져오기 (여러 selector 시도)
            post_text = ""
            post_selectors = [
                "//div[contains(@class, '_a9zs')]//span",
                "//article//div[contains(@class, '_a9zr')]//span",
                "//div[@data-testid='post-content']//span",
                "//article//span[contains(@dir, 'auto')]",
                "//div[contains(@class, 'x1lliihq')]//span"
            ]

            for selector in post_selectors:
                try:
                    post_element = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    post_text = post_element.text
                    break
                except:
                    continue

            # 해시태그 가져오기 (개선된 selector)
            hashtags = []
            hashtag_selectors = [
                "//a[contains(@href, '/explore/tags/')]",
                "//a[starts-with(@href, '/explore/tags/')]",
                "//span[starts-with(text(), '#')]"
            ]

            for selector in hashtag_selectors:
                try:
                    hashtag_elements = self.driver.find_elements(By.XPATH, selector)
                    for element in hashtag_elements:
                        tag = element.text.replace('#', '').strip()
                        if tag and tag not in hashtags:
                            hashtags.append(tag)
                except:
                    continue

            # AI로 댓글 생성
            if config.USE_AI_COMMENTS and post_text:
                comment = self.gemini.generate_comment(post_text, hashtags)
            else:
                comment = random.choice(config.COMMENTS)

        # 이모지 및 특수 유니코드 문자 제거
        safe_comment = self._remove_emojis(comment)
        if safe_comment != comment:
            logger.info(f"이모지 또는 특수 문자가 제거되었습니다. 원본: '{comment}', 변환: '{safe_comment}'")
            comment = safe_comment

        # 댓글이 비어있으면 기본 댓글 사용
        if not comment.strip():
            comment = random.choice(config.COMMENTS)
            logger.info(f"댓글이 비어있어 기본 댓글을 사용합니다: {comment}")

        try:
            # 댓글 입력창 찾기 (여러 selector 시도)
            comment_selectors = [
                "//textarea[contains(@placeholder, '댓글') or contains(@placeholder, 'comment')]",
                "//textarea[@aria-label='댓글 달기...' or @aria-label='Add a comment...']",
                "//div[@role='textbox'][contains(@aria-label, '댓글') or contains(@aria-label, 'comment')]",
                "//form//textarea",
                "//textarea[contains(@aria-label, '댓글') or contains(@aria-label, 'comment')]"
            ]

            comment_input = None
            for selector in comment_selectors:
                try:
                    comment_input = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    break
                except:
                    continue

            if not comment_input:
                logger.error("댓글 입력 필드를 찾을 수 없습니다.")
                return False

            # 댓글 입력창 클릭 및 포커스
            comment_input.click()
            time.sleep(0.5)
            comment_input.clear()

            # 댓글 입력 (자연스럽게)
            comment_input.send_keys(comment)
            time.sleep(1)

            # 게시 버튼 찾기 및 클릭 (여러 selector 시도)
            post_selectors = [
                "//button[contains(text(), '게시') or contains(text(), 'Post')]",
                "//div[contains(text(), '게시') or contains(text(), 'Post')][@role='button']",
                "//button[@type='submit']",
                "//button[contains(@aria-label, '게시') or contains(@aria-label, 'Post')]",
                "//form//button[last()]"
            ]

            for selector in post_selectors:
                try:
                    post_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    post_button.click()
                    logger.info(f"댓글 작성 성공: {comment}")
                    time.sleep(random.uniform(1, 2))  # 자연스러운 지연
                    return True
                except:
                    continue

            logger.error("게시 버튼을 찾을 수 없습니다.")
            return False

        except Exception as e:
            logger.error(f"댓글 작성 중 오류 발생: {e}")
            return False

    def follow_user(self, username=None):
        """특정 사용자 팔로우"""
        if username:
            # 사용자 프로필 페이지로 이동
            try:
                logger.info(f"사용자 '{username}' 프로필로 이동 중...")
                self.driver.get(f"{self.base_url}{username}/")
                time.sleep(3)  # 페이지 로딩 대기
            except Exception as e:
                logger.error(f"사용자 프로필 페이지 이동 중 오류 발생: {e}")
                return False

        try:
            # 팔로우 버튼 찾기 (개선된 selector들)
            follow_selectors = [
                "//button[contains(text(), '팔로우') or contains(text(), 'Follow')]",
                "//div[contains(text(), '팔로우') or contains(text(), 'Follow')]/parent::button",
                "//div[@role='button'][contains(text(), '팔로우') or contains(text(), 'Follow')]",
                "//button[contains(@aria-label, '팔로우') or contains(@aria-label, 'Follow')]",
                "//header//button[contains(text(), '팔로우') or contains(text(), 'Follow')]",
                "//section//button[contains(text(), '팔로우') or contains(text(), 'Follow')]"
            ]

            follow_button = None
            button_text = ""

            for selector in follow_selectors:
                try:
                    follow_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    button_text = follow_button.text.lower()
                    break
                except:
                    continue

            if not follow_button:
                logger.error("팔로우 버튼을 찾을 수 없습니다.")
                return False

            # 이미 팔로우 중인지 확인
            if '팔로우' in button_text or 'follow' in button_text:
                if '팔로우 중' in button_text or 'following' in button_text or '언팔로우' in button_text or 'unfollow' in button_text:
                    logger.info(f"이미 {username if username else '이 사용자를'} 팔로우하고 있습니다.")
                    return False
                else:
                    # 팔로우 버튼 클릭
                    follow_button.click()
                    logger.info(f"사용자{' ' + username if username else ''} 팔로우 성공!")
                    time.sleep(random.uniform(1, 2))  # 자연스러운 지연
                    return True
            else:
                logger.info(f"이미 {username if username else '이 사용자를'} 팔로우하고 있습니다.")
                return False
        except Exception as e:
            logger.error(f"팔로우 중 오류 발생: {e}")

            # 세션 오류인 경우 브라우저 재시작
            if "session" in str(e).lower() or "connection" in str(e).lower():
                logger.info("세션 오류 감지. 브라우저 재시작 중...")
                try:
                    self.driver.quit()

                    # 새 브라우저 세션 시작
                    chrome_options = Options()
                    chrome_options.add_argument("--no-sandbox")
                    chrome_options.add_argument("--disable-dev-shm-usage")
                    chrome_options.add_argument("--disable-notifications")
                    chrome_options.add_argument("--disable-popup-blocking")
                    chrome_options.add_argument("--start-maximized")
                    chrome_options.add_argument("--disable-infobars")
                    chrome_options.add_argument("--disable-extensions")
                    chrome_options.add_argument("--disable-gpu")
                    chrome_options.add_argument("--lang=ko-KR")
                    chrome_options.add_argument("--log-level=3")  # 로그 레벨 최소화

                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    self.driver.implicitly_wait(config.IMPLICIT_WAIT)

                    # 다시 로그인
                    self.logged_in = False
                    if self.login() and username:
                        # 다시 사용자 프로필로 이동
                        return self.follow_user(username)
                except Exception as e2:
                    logger.error(f"브라우저 재시작 중 오류 발생: {e2}")

            return False

    def interact_with_post(self, post_url):
        """게시물과 상호작용 (좋아요, 댓글, 팔로우)"""
        try:
            logger.info(f"게시물 상호작용 중: {post_url}")
            self.driver.get(post_url)
            time.sleep(3)  # 페이지 로딩 대기

            # 좋아요
            liked = self.like_post()

            # 댓글 (일정 확률로)
            if random.random() < 0.7:  # 70% 확률로 댓글 작성
                commented = self.comment_post()
            else:
                commented = False
                logger.info("이번 게시물은 댓글을 작성하지 않습니다.")

            # 팔로우 (일정 확률로)
            if liked and random.random() < config.FOLLOW_RATIO:
                followed = self.follow_user()
            else:
                followed = False
                logger.info("이번 게시물은 팔로우하지 않습니다.")

            return {
                "url": post_url,
                "liked": liked,
                "commented": commented,
                "followed": followed
            }
        except Exception as e:
            logger.error(f"게시물 상호작용 중 오류 발생: {e}")
            return {
                "url": post_url,
                "liked": False,
                "commented": False,
                "followed": False,
                "error": str(e)
            }

    def upload_post(self, image_path, caption=None, topic=None):
        """게시물 업로드"""
        if not self.logged_in and not self.login():
            logger.error("로그인이 필요합니다.")
            return False

        if not os.path.exists(image_path):
            logger.error(f"이미지 파일이 존재하지 않습니다: {image_path}")
            return False

        try:
            logger.info("게시물 업로드 시작...")

            # 캡션 생성 (없는 경우)
            if not caption and config.USE_AI_CAPTIONS:
                if not topic and config.POST_TOPICS:
                    topic = random.choice(config.POST_TOPICS)
                caption = self.gemini.generate_caption(image_path, topic)

            # 모바일 버전으로 전환 (더 안정적인 업로드)
            self.driver.get("https://www.instagram.com/")
            time.sleep(5)  # 페이지 로딩 충분히 대기

            # 모바일 에뮬레이션 설정
            mobile_emulation = {
                "deviceMetrics": {"width": 375, "height": 812, "pixelRatio": 3.0},
                "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
            }

            # 새 브라우저 세션 시작 (모바일 에뮬레이션 적용)
            self.driver.quit()
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-notifications")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-infobars")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--log-level=3")  # 로그 레벨 최소화
            chrome_options.add_experimental_option("mobileEmulation", mobile_emulation)

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)

            # 다시 로그인
            logger.info("모바일 모드로 전환하여 다시 로그인 중...")
            self.logged_in = False
            if not self.login():
                logger.error("모바일 모드에서 로그인 실패")
                return False

            # 업로드 버튼 클릭 (모바일 버전)
            logger.info("업로드 버튼 찾는 중...")
            try:
                # 하단 메뉴에서 + 버튼 찾기
                create_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//div[@role='tablist']//a[contains(@href, 'create')]"))
                )
                create_button.click()
                logger.info("업로드 버튼 클릭 성공")
                time.sleep(3)
            except Exception as e:
                logger.error(f"업로드 버튼을 찾을 수 없습니다: {e}")

                # 다른 방법 시도: 직접 create 페이지로 이동
                try:
                    self.driver.get("https://www.instagram.com/create/select/")
                    time.sleep(3)
                    logger.info("직접 create 페이지로 이동 성공")
                except:
                    logger.error("직접 create 페이지로 이동 실패")
                    return False

            # 파일 선택 (input 요소 찾기)
            try:
                logger.info("파일 입력 필드 찾는 중...")
                file_input = WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.XPATH, "//input[@type='file']"))
                )

                # 파일 경로 전송
                file_input.send_keys(os.path.abspath(image_path))
                logger.info(f"이미지 파일 선택 성공: {image_path}")
                time.sleep(5)  # 파일 업로드 대기 시간 증가
            except Exception as e:
                logger.error(f"파일 선택 중 오류 발생: {e}")
                return False

            # 다음 버튼 클릭 (여러 단계가 있을 수 있음)
            for i in range(3):  # 최대 3번 다음 버튼 클릭
                try:
                    logger.info(f"다음 버튼 찾는 중... ({i+1}/3)")
                    next_button = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '다음') or contains(text(), 'Next')]"))
                    )
                    next_button.click()
                    logger.info(f"다음 버튼 클릭 성공 ({i+1}/3)")
                    time.sleep(3)  # 대기 시간 증가
                except:
                    logger.info(f"더 이상 다음 버튼이 없습니다. ({i+1}/3)")
                    break

            # 캡션 입력
            try:
                logger.info("캡션 입력 필드 찾는 중...")
                # 이모지 및 특수 유니코드 문자 제거
                safe_caption = self._remove_emojis(caption)
                if safe_caption != caption:
                    logger.info(f"캡션에서 이모지 또는 특수 문자가 제거되었습니다.")
                    caption = safe_caption

                # 여러 XPath 시도
                caption_textarea = None
                try:
                    caption_textarea = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//textarea[contains(@placeholder, '문구 입력') or contains(@placeholder, 'Write a caption')]"))
                    )
                except:
                    try:
                        caption_textarea = WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, "//div[contains(@aria-label, '문구 입력') or contains(@aria-label, 'Write a caption')]"))
                        )
                    except:
                        try:
                            caption_textarea = WebDriverWait(self.driver, 10).until(
                                EC.presence_of_element_located((By.XPATH, "//div[@role='textbox']"))
                            )
                        except:
                            # 모바일 버전에서 다른 XPath 시도
                            caption_textarea = WebDriverWait(self.driver, 10).until(
                                EC.presence_of_element_located((By.XPATH, "//textarea"))
                            )

                if caption_textarea:
                    caption_textarea.clear()

                    # 한 글자씩 입력 (더 안정적인 방법)
                    for char in caption:
                        caption_textarea.send_keys(char)
                        time.sleep(0.01)  # 약간의 지연

                    logger.info("캡션 입력 성공")
                    time.sleep(3)  # 대기 시간 증가
                else:
                    logger.warning("캡션 입력 필드를 찾을 수 없습니다.")
            except Exception as e:
                logger.warning(f"캡션 입력 중 오류 발생: {e}")

            # 공유 버튼 클릭 (여러 XPath 시도)
            try:
                logger.info("공유 버튼 찾는 중...")
                share_button = None
                try:
                    share_button = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '공유') or contains(text(), 'Share')]"))
                    )
                except:
                    try:
                        share_button = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '공유하기') or contains(text(), 'Share')]/parent::button"))
                        )
                    except:
                        try:
                            share_button = WebDriverWait(self.driver, 10).until(
                                EC.element_to_be_clickable((By.XPATH, "//button[@type='submit']"))
                            )
                        except:
                            # 모바일 버전에서 다른 XPath 시도
                            share_button = WebDriverWait(self.driver, 10).until(
                                EC.element_to_be_clickable((By.XPATH, "//div[@role='button'][contains(text(), '공유') or contains(text(), 'Share')]"))
                            )

                if share_button:
                    share_button.click()
                    logger.info("공유 버튼 클릭 성공")
                    time.sleep(15)  # 업로드 완료 대기 시간 증가
                else:
                    logger.error("공유 버튼을 찾을 수 없습니다.")
                    return False

                # 업로드 완료 확인 (여러 방법 시도)
                upload_success = False

                # 방법 1: 완료 메시지 확인
                try:
                    WebDriverWait(self.driver, 20).until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(text(), '게시물이 공유되었습니다') or contains(text(), 'Your post has been shared')]"))
                    )
                    upload_success = True
                    logger.info("완료 메시지 확인됨")
                except:
                    logger.info("완료 메시지를 찾을 수 없음")

                # 방법 2: 홈페이지로 리디렉션 확인
                if not upload_success:
                    try:
                        WebDriverWait(self.driver, 10).until(
                            EC.url_contains("instagram.com/")
                        )
                        upload_success = True
                        logger.info("홈페이지로 리디렉션 확인됨")
                    except:
                        logger.info("홈페이지로 리디렉션 확인 실패")

                # 방법 3: 오류 메시지가 없는지 확인
                if not upload_success:
                    try:
                        error_message = self.driver.find_element(By.XPATH, "//p[contains(text(), '문제가 발생했습니다') or contains(text(), 'problem')]")
                        if error_message:
                            logger.error(f"업로드 중 오류 메시지 발견: {error_message.text}")
                            return False
                    except:
                        # 오류 메시지가 없으면 성공으로 간주
                        upload_success = True
                        logger.info("오류 메시지가 없음 - 업로드 성공으로 간주")

                if upload_success:
                    logger.info("게시물 업로드 성공!")

                    # 원래 브라우저 설정으로 복원
                    self.driver.quit()
                    chrome_options = Options()
                    chrome_options.add_argument("--no-sandbox")
                    chrome_options.add_argument("--disable-dev-shm-usage")
                    chrome_options.add_argument("--disable-notifications")
                    chrome_options.add_argument("--disable-popup-blocking")
                    chrome_options.add_argument("--start-maximized")
                    chrome_options.add_argument("--disable-infobars")
                    chrome_options.add_argument("--disable-extensions")
                    chrome_options.add_argument("--disable-gpu")
                    chrome_options.add_argument("--lang=ko-KR")
                    chrome_options.add_argument("--log-level=3")  # 로그 레벨 최소화

                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    self.driver.implicitly_wait(config.IMPLICIT_WAIT)

                    # 다시 로그인
                    self.logged_in = False
                    self.login()

                    return True
                else:
                    logger.warning("업로드 완료 확인을 할 수 없지만, 업로드는 진행되었을 수 있습니다.")

                    # 원래 브라우저 설정으로 복원
                    self.driver.quit()
                    chrome_options = Options()
                    chrome_options.add_argument("--no-sandbox")
                    chrome_options.add_argument("--disable-dev-shm-usage")
                    chrome_options.add_argument("--disable-notifications")
                    chrome_options.add_argument("--disable-popup-blocking")
                    chrome_options.add_argument("--start-maximized")
                    chrome_options.add_argument("--disable-infobars")
                    chrome_options.add_argument("--disable-extensions")
                    chrome_options.add_argument("--disable-gpu")
                    chrome_options.add_argument("--lang=ko-KR")
                    chrome_options.add_argument("--log-level=3")  # 로그 레벨 최소화

                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    self.driver.implicitly_wait(config.IMPLICIT_WAIT)

                    # 다시 로그인
                    self.logged_in = False
                    self.login()

                    return True

            except Exception as e:
                logger.error(f"공유 버튼 클릭 중 오류 발생: {e}")

                # 원래 브라우저 설정으로 복원
                self.driver.quit()
                chrome_options = Options()
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument("--disable-notifications")
                chrome_options.add_argument("--disable-popup-blocking")
                chrome_options.add_argument("--start-maximized")
                chrome_options.add_argument("--disable-infobars")
                chrome_options.add_argument("--disable-extensions")
                chrome_options.add_argument("--disable-gpu")
                chrome_options.add_argument("--lang=ko-KR")
                chrome_options.add_argument("--log-level=3")  # 로그 레벨 최소화

                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                self.driver.implicitly_wait(config.IMPLICIT_WAIT)

                # 다시 로그인
                self.logged_in = False
                self.login()

                return False

        except Exception as e:
            logger.error(f"게시물 업로드 중 오류 발생: {e}")

            # 원래 브라우저 설정으로 복원
            try:
                self.driver.quit()
                chrome_options = Options()
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument("--disable-notifications")
                chrome_options.add_argument("--disable-popup-blocking")
                chrome_options.add_argument("--start-maximized")
                chrome_options.add_argument("--disable-infobars")
                chrome_options.add_argument("--disable-extensions")
                chrome_options.add_argument("--disable-gpu")
                chrome_options.add_argument("--lang=ko-KR")
                chrome_options.add_argument("--log-level=3")  # 로그 레벨 최소화

                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                self.driver.implicitly_wait(config.IMPLICIT_WAIT)

                # 다시 로그인
                self.logged_in = False
                self.login()
            except:
                pass

            return False

    def run_hashtag_interaction(self, hashtags=None, posts_per_hashtag=None):
        """해시태그 기반 상호작용 실행"""
        if not hashtags:
            hashtags = config.HASHTAGS

        if not posts_per_hashtag:
            posts_per_hashtag = config.LIKE_PER_HASHTAG

        if not self.logged_in and not self.login():
            logger.error("로그인이 필요합니다.")
            return False

        results = {
            "total_interactions": 0,
            "likes": 0,
            "comments": 0,
            "follows": 0,
            "details": []
        }

        try:
            # 각 해시태그에 대해 처리
            for hashtag in hashtags:
                logger.info(f"해시태그 #{hashtag} 처리 중...")

                # 해시태그 검색
                if not self.search_hashtag(hashtag):
                    continue

                # 게시물 링크 가져오기
                post_links = self.get_posts(posts_per_hashtag)

                # 각 게시물과 상호작용
                for post_url in post_links:
                    result = self.interact_with_post(post_url)
                    results["details"].append(result)

                    if result.get("liked", False):
                        results["likes"] += 1

                    if result.get("commented", False):
                        results["comments"] += 1

                    if result.get("followed", False):
                        results["follows"] += 1

                    results["total_interactions"] += 1

                    # 자연스러운 지연
                    delay = random.uniform(30, 60)  # 30-60초 지연
                    logger.info(f"다음 게시물까지 {delay:.1f}초 대기 중...")
                    time.sleep(delay)

                # 해시태그 간 지연
                if hashtag != hashtags[-1]:
                    delay = random.uniform(60, 120)  # 1-2분 지연
                    logger.info(f"다음 해시태그까지 {delay:.1f}초 대기 중...")
                    time.sleep(delay)

            logger.info(f"상호작용 완료: 좋아요 {results['likes']}개, 댓글 {results['comments']}개, 팔로우 {results['follows']}개")
            return results

        except Exception as e:
            logger.error(f"해시태그 상호작용 중 오류 발생: {e}")
            return results

    def get_following_list(self, max_count=50):
        """팔로잉 목록 가져오기"""
        if not self.logged_in and not self.login():
            logger.error("로그인이 필요합니다.")
            return []

        try:
            # 프로필 페이지로 이동
            logger.info("프로필 페이지로 이동 중...")
            self.driver.get(f"{self.base_url}{self.username}/")
            time.sleep(3)  # 페이지 로딩 대기

            # 팔로잉 수 클릭
            following_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(@href, '/following')]"))
            )
            following_count_text = following_button.text.split()[0].replace(',', '')
            following_count = int(following_count_text) if following_count_text.isdigit() else 0
            logger.info(f"팔로잉 수: {following_count}")

            # 팔로잉 목록 열기
            following_button.click()
            time.sleep(2)

            # 팔로잉 목록 스크롤
            following_list = []
            scroll_box = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[@role='dialog']//div[@style='height: 356px; overflow: hidden auto;']"))
            )

            last_height = self.driver.execute_script("return arguments[0].scrollHeight", scroll_box)
            while len(following_list) < min(following_count, max_count):
                # 팔로잉 사용자 요소 가져오기
                user_elements = scroll_box.find_elements(By.XPATH, ".//div[contains(@class, '_aano')]//div[contains(@class, '_ab8y')]//div[contains(@class, '_ab8w')]//div[contains(@class, '_ab8w')]//span")

                for element in user_elements:
                    username = element.text
                    if username and username not in following_list:
                        following_list.append(username)

                    if len(following_list) >= max_count:
                        break

                # 스크롤 다운
                self.driver.execute_script("arguments[0].scrollTo(0, arguments[0].scrollHeight)", scroll_box)
                time.sleep(1)

                # 스크롤이 더 이상 내려가지 않는지 확인
                new_height = self.driver.execute_script("return arguments[0].scrollHeight", scroll_box)
                if new_height == last_height:
                    break
                last_height = new_height

            # 팔로잉 목록 닫기
            try:
                close_button = self.driver.find_element(By.XPATH, "//div[@role='dialog']//button[contains(@class, '_abl-')]")
                close_button.click()
                time.sleep(1)
            except:
                logger.warning("팔로잉 목록 닫기 버튼을 찾을 수 없습니다.")

            logger.info(f"{len(following_list)}명의 팔로잉 목록을 가져왔습니다.")
            return following_list

        except Exception as e:
            logger.error(f"팔로잉 목록 가져오기 중 오류 발생: {e}")
            return []

    def get_followers_list(self, max_count=50):
        """팔로워 목록 가져오기"""
        if not self.logged_in and not self.login():
            logger.error("로그인이 필요합니다.")
            return []

        try:
            # 프로필 페이지로 이동
            logger.info("프로필 페이지로 이동 중...")
            self.driver.get(f"{self.base_url}{self.username}/")
            time.sleep(3)  # 페이지 로딩 대기

            # 팔로워 수 클릭
            followers_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(@href, '/followers')]"))
            )
            followers_count_text = followers_button.text.split()[0].replace(',', '')
            followers_count = int(followers_count_text) if followers_count_text.isdigit() else 0
            logger.info(f"팔로워 수: {followers_count}")

            # 팔로워 목록 열기
            followers_button.click()
            time.sleep(2)

            # 팔로워 목록 스크롤
            followers_list = []
            scroll_box = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[@role='dialog']//div[@style='height: 356px; overflow: hidden auto;']"))
            )

            last_height = self.driver.execute_script("return arguments[0].scrollHeight", scroll_box)
            while len(followers_list) < min(followers_count, max_count):
                # 팔로워 사용자 요소 가져오기
                user_elements = scroll_box.find_elements(By.XPATH, ".//div[contains(@class, '_aano')]//div[contains(@class, '_ab8y')]//div[contains(@class, '_ab8w')]//div[contains(@class, '_ab8w')]//span")

                for element in user_elements:
                    username = element.text
                    if username and username not in followers_list:
                        followers_list.append(username)

                    if len(followers_list) >= max_count:
                        break

                # 스크롤 다운
                self.driver.execute_script("arguments[0].scrollTo(0, arguments[0].scrollHeight)", scroll_box)
                time.sleep(1)

                # 스크롤이 더 이상 내려가지 않는지 확인
                new_height = self.driver.execute_script("return arguments[0].scrollHeight", scroll_box)
                if new_height == last_height:
                    break
                last_height = new_height

            # 팔로워 목록 닫기
            try:
                close_button = self.driver.find_element(By.XPATH, "//div[@role='dialog']//button[contains(@class, '_abl-')]")
                close_button.click()
                time.sleep(1)
            except:
                logger.warning("팔로워 목록 닫기 버튼을 찾을 수 없습니다.")

            logger.info(f"{len(followers_list)}명의 팔로워 목록을 가져왔습니다.")
            return followers_list

        except Exception as e:
            logger.error(f"팔로워 목록 가져오기 중 오류 발생: {e}")
            return []

    def unfollow_user(self, username):
        """특정 사용자 언팔로우"""
        if not self.logged_in and not self.login():
            logger.error("로그인이 필요합니다.")
            return False

        try:
            # 사용자 프로필 페이지로 이동
            logger.info(f"사용자 '{username}' 프로필로 이동 중...")
            self.driver.get(f"{self.base_url}{username}/")
            time.sleep(3)  # 페이지 로딩 대기

            # 팔로잉/팔로우 버튼 찾기
            try:
                # 팔로잉 버튼 찾기 (이미 팔로우 중인 경우)
                following_button = WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//button[contains(@class, '_acan') and not(contains(text(), '팔로우')) and not(contains(text(), 'Follow'))]"))
                )

                # 팔로잉 버튼 클릭
                following_button.click()
                time.sleep(1)

                # 언팔로우 확인 버튼 클릭
                unfollow_confirm = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '팔로우 취소') or contains(text(), 'Unfollow')]"))
                )
                unfollow_confirm.click()

                logger.info(f"사용자 '{username}' 언팔로우 성공!")
                time.sleep(1)
                return True

            except:
                logger.warning(f"사용자 '{username}'을(를) 팔로우하고 있지 않습니다.")
                return False

        except Exception as e:
            logger.error(f"언팔로우 중 오류 발생: {e}")
            return False

    def find_non_followers(self, max_count=50):
        """나를 팔로우하지 않는 팔로잉 목록 찾기"""
        try:
            # 팔로잉 목록 가져오기
            following_list = self.get_following_list(max_count)

            # 팔로워 목록 가져오기
            followers_list = self.get_followers_list(max_count)

            # 나를 팔로우하지 않는 사용자 찾기
            non_followers = [user for user in following_list if user not in followers_list]

            logger.info(f"{len(non_followers)}명이 나를 팔로우하지 않고 있습니다.")
            return non_followers

        except Exception as e:
            logger.error(f"팔로우하지 않는 사용자 찾기 중 오류 발생: {e}")
            return []

    def send_dm(self, username, message):
        """특정 사용자에게 DM 보내기 - 개선된 버전"""
        if not self.logged_in and not self.login():
            logger.error("로그인이 필요합니다.")
            return False

        try:
            logger.info(f"사용자 '{username}'에게 DM 보내는 중...")

            # 직접 DM 페이지로 이동 (모바일 모드 전환 없이)
            dm_url = f"{self.base_url}direct/new/"
            self.driver.get(dm_url)
            time.sleep(2)

            # 사용자 검색 및 선택
            if not self._search_and_select_user(username):
                return False

            # 메시지 전송
            return self._send_message(message, username)

        except Exception as e:
            logger.error(f"DM 전송 중 오류 발생: {e}")
            return False

    def _search_and_select_user(self, username):
        """사용자 검색 및 선택 - 개선된 버전"""
        try:
            # 페이지 로딩 대기
            time.sleep(2)

            # 검색 입력 필드 찾기 (최신 Instagram UI 대응)
            search_selectors = [
                "//input[@placeholder='검색...' or @placeholder='Search...']",
                "//input[contains(@placeholder, '검색') or contains(@placeholder, 'Search')]",
                "//input[@type='text']",
                "//div[@role='textbox']",
                "//input[contains(@aria-label, '검색') or contains(@aria-label, 'Search')]"
            ]

            search_input = None
            for selector in search_selectors:
                try:
                    search_input = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    logger.info(f"검색 입력 필드 찾음: {selector}")
                    break
                except:
                    continue

            if not search_input:
                logger.error("검색 입력 필드를 찾을 수 없습니다.")
                return False

            # 검색 필드 클릭 및 포커스
            search_input.click()
            time.sleep(0.5)
            search_input.clear()

            # 사용자명 입력 (자연스럽게)
            search_input.send_keys(username)
            logger.info(f"사용자명 '{username}' 입력 완료")
            time.sleep(3)  # 검색 결과 로딩 대기

            # 검색 결과에서 사용자 선택 (개선된 selector)
            user_selectors = [
                f"//div[contains(@aria-label, '{username}')]",
                f"//span[contains(text(), '{username}')]/ancestor::div[@role='button']",
                f"//div[@role='button'][.//span[contains(text(), '{username}')]]",
                f"//*[contains(text(), '{username}')]/ancestor::div[@role='button']",
                f"//div[contains(@class, 'x1i10hfl')][.//span[contains(text(), '{username}')]]"
            ]

            user_element = None
            for selector in user_selectors:
                try:
                    user_element = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    logger.info(f"사용자 요소 찾음: {selector}")
                    break
                except:
                    continue

            if not user_element:
                logger.error(f"사용자 '{username}'을 찾을 수 없습니다.")
                return False

            user_element.click()
            logger.info(f"사용자 '{username}' 선택 성공")
            time.sleep(2)

            # 다음 버튼 클릭 (개선된 selector)
            next_selectors = [
                "//button[contains(text(), '다음') or contains(text(), 'Next')]",
                "//div[contains(text(), '다음') or contains(text(), 'Next')][@role='button']",
                "//button[contains(@aria-label, '다음') or contains(@aria-label, 'Next')]",
                "//div[contains(@class, 'x1i10hfl')][contains(text(), '다음') or contains(text(), 'Next')]"
            ]

            for selector in next_selectors:
                try:
                    next_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    next_button.click()
                    logger.info("다음 버튼 클릭 성공")
                    time.sleep(2)
                    return True
                except:
                    continue

            logger.error("다음 버튼을 찾을 수 없습니다.")
            return False

        except Exception as e:
            logger.error(f"사용자 검색 중 오류 발생: {e}")
            return False

    def _send_message(self, message, username):
        """메시지 전송 - 개선된 버전"""
        try:
            # 페이지 로딩 대기
            time.sleep(2)

            # 메시지 입력 필드 찾기 (최신 Instagram UI 대응)
            message_selectors = [
                "//div[@role='textbox']",
                "//textarea[contains(@placeholder, '메시지') or contains(@placeholder, 'Message')]",
                "//div[contains(@aria-label, '메시지') or contains(@aria-label, 'Message')][@role='textbox']",
                "//input[@type='text'][contains(@placeholder, '메시지') or contains(@placeholder, 'Message')]",
                "//div[contains(@class, 'x1i10hfl')][@role='textbox']",
                "//textarea",
                "//input[@type='text']"
            ]

            message_input = None
            for selector in message_selectors:
                try:
                    message_input = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    logger.info(f"메시지 입력 필드 찾음: {selector}")
                    break
                except:
                    continue

            if not message_input:
                logger.error("메시지 입력 필드를 찾을 수 없습니다.")
                return False

            # 메시지 입력 필드 클릭 및 포커스
            message_input.click()
            time.sleep(0.5)

            # 이모지 제거 및 메시지 입력
            safe_message = self._remove_emojis(message)
            message_input.clear()
            message_input.send_keys(safe_message)
            logger.info(f"메시지 입력 완료: {safe_message}")
            time.sleep(1)

            # 보내기 버튼 클릭 (개선된 selector)
            send_selectors = [
                "//button[contains(text(), '보내기') or contains(text(), 'Send')]",
                "//div[contains(text(), '보내기') or contains(text(), 'Send')][@role='button']",
                "//*[local-name()='svg' and (@aria-label='메시지 보내기' or @aria-label='Send message')]/ancestor::button",
                "//*[local-name()='svg' and (@aria-label='메시지 보내기' or @aria-label='Send message')]/parent::*",
                "//button[contains(@aria-label, '보내기') or contains(@aria-label, 'Send')]",
                "//div[contains(@class, 'x1i10hfl')][contains(text(), '보내기') or contains(text(), 'Send')]"
            ]

            for selector in send_selectors:
                try:
                    send_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    send_button.click()
                    logger.info(f"사용자 '{username}'에게 DM 전송 성공!")
                    time.sleep(2)
                    return True
                except:
                    continue

            logger.error("보내기 버튼을 찾을 수 없습니다.")
            return False

        except Exception as e:
            logger.error(f"메시지 전송 중 오류 발생: {e}")
            return False



    def send_group_dm(self, usernames, message):
        """여러 사용자에게 그룹 DM 보내기 - 개선된 버전"""
        if not self.logged_in and not self.login():
            logger.error("로그인이 필요합니다.")
            return False

        if not usernames or len(usernames) == 0:
            logger.error("사용자 목록이 비어있습니다.")
            return False

        try:
            logger.info(f"{len(usernames)}명의 사용자에게 그룹 DM 보내는 중...")

            # 직접 DM 페이지로 이동
            dm_url = f"{self.base_url}direct/new/"
            self.driver.get(dm_url)
            time.sleep(2)

            # 각 사용자 검색 및 선택
            selected_users = []
            for username in usernames:
                if self._search_and_select_user_for_group(username):
                    selected_users.append(username)

            if not selected_users:
                logger.error("선택된 사용자가 없습니다.")
                return False

            logger.info(f"{len(selected_users)}명의 사용자가 선택되었습니다.")

            # 다음 버튼 클릭 후 메시지 전송
            return self._send_group_message(message, selected_users)

        except Exception as e:
            logger.error(f"그룹 DM 전송 중 오류 발생: {e}")
            return False

    def _search_and_select_user_for_group(self, username):
        """그룹 DM용 사용자 검색 및 선택"""
        try:
            # 검색 입력 필드 찾기
            search_selectors = [
                "//input[@placeholder='검색...' or @placeholder='Search...']",
                "//input[@type='text']",
                "//input[contains(@placeholder, '검색')]"
            ]

            search_input = None
            for selector in search_selectors:
                try:
                    search_input = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    break
                except:
                    continue

            if not search_input:
                logger.error("검색 입력 필드를 찾을 수 없습니다.")
                return False

            # 사용자명 입력
            search_input.clear()
            search_input.send_keys(username)
            time.sleep(2)  # 검색 결과 로딩 대기

            # 검색 결과에서 사용자 선택
            user_selectors = [
                f"//div[contains(@aria-label, '{username}')]",
                f"//span[contains(text(), '{username}')]/ancestor::div[contains(@role, 'button')]",
                f"//*[contains(text(), '{username}')]"
            ]

            for selector in user_selectors:
                try:
                    user_element = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    user_element.click()
                    logger.info(f"사용자 '{username}' 선택 성공")
                    time.sleep(1)
                    return True
                except:
                    continue

            logger.error(f"사용자 '{username}'을 찾을 수 없습니다.")
            return False

        except Exception as e:
            logger.error(f"사용자 검색 중 오류 발생: {e}")
            return False

    def _send_group_message(self, message, selected_users):
        """그룹 메시지 전송"""
        try:
            # 다음 버튼 클릭
            next_selectors = [
                "//button[contains(text(), '다음') or contains(text(), 'Next')]",
                "//div[contains(text(), '다음') or contains(text(), 'Next')]",
                "//button[@type='button'][last()]"
            ]

            for selector in next_selectors:
                try:
                    next_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    next_button.click()
                    logger.info("다음 버튼 클릭 성공")
                    time.sleep(1)
                    break
                except:
                    continue
            else:
                logger.error("다음 버튼을 찾을 수 없습니다.")
                return False

            # 메시지 입력 및 전송
            return self._send_message(message, f"{len(selected_users)}명의 사용자")

        except Exception as e:
            logger.error(f"그룹 메시지 전송 중 오류 발생: {e}")
            return False

    def close(self):
        """브라우저 종료"""
        try:
            self.driver.quit()
            logger.info("브라우저가 종료되었습니다.")
        except Exception as e:
            logger.error(f"브라우저 종료 중 오류 발생: {e}")
