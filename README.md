# 인스타그램 자동화 프로그램

Gemini API를 활용한 인스타그램 자동화 프로그램입니다. 이 프로그램은 다음과 같은 기능을 제공합니다:

- 해시태그 기반 게시물 자동 좋아요
- AI를 활용한 지능적인 댓글 자동 작성
- 자동 팔로우/언팔로우
- AI를 활용한 게시물 캡션 생성 및 업로드
- 대화형 모드로 원하는 게시물 직접 업로드
- 직접 작성한 글로 게시물 업로드
- 특정 게시물에 댓글 작성
- 팔로잉/팔로워 목록 관리
- 맞팔로우하지 않는 사용자 찾기 및 자동 언팔로우
- 개인 및 그룹 DM 보내기
- AI를 활용한 DM 메시지 생성 및 전송

## 설치 방법

1. 필요한 패키지 설치:
```bash
pip install -r requirements.txt
```

2. 환경 변수 설정:
`.env.example` 파일을 참고하여 `.env` 파일을 생성하고 필요한 정보를 입력하세요.
```
# 인스타그램 계정 정보
INSTAGRAM_USERNAME=your_instagram_username
INSTAGRAM_PASSWORD=your_instagram_password

# Gemini API 키
GEMINI_API_KEY=your_gemini_api_key
```

3. 이미지 폴더 생성:
게시물 업로드를 위한 이미지 폴더를 생성하세요.
```bash
mkdir images
```

## 사용 방법

### 대화형 모드 (권장)

대화형 모드는 메뉴를 통해 쉽게 인스타그램 자동화 기능을 사용할 수 있습니다.

```bash
python main.py --mode interactive
```

대화형 모드에서는 다음과 같은 기능을 사용할 수 있습니다:
- 해시태그 기반 자동 상호작용
- AI 캡션으로 게시물 업로드
- 직접 작성한 캡션으로 게시물 업로드
- 특정 게시물에 댓글 작성
- 특정 사용자 팔로우
- 팔로잉 관리 (팔로잉/팔로워 목록 보기, 언팔로우, 맞팔 확인)
- DM 보내기 (개인 DM, 그룹 DM, AI 생성 메시지)
- 설정 변경

### 해시태그 기반 상호작용

```bash
python main.py --mode interact --hashtags 여행 맛집 일상 --posts 5
```

### 게시물 업로드

```bash
# 폴더에서 랜덤 이미지 선택하여 업로드
python main.py --mode upload --image-folder images --topic 여행

# 특정 이미지 파일 업로드 (AI 캡션 생성)
python main.py --mode upload --image path/to/image.jpg --topic 여행

# 특정 이미지 파일 업로드 (직접 캡션 작성)
python main.py --mode upload --image path/to/image.jpg --caption "나의 여행 사진 #여행 #일상"
```

### 상호작용 및 게시물 업로드 동시 실행

```bash
python main.py --mode both --hashtags 여행 맛집 --posts 3 --image-folder images --topic 일상
```

## 설정 옵션

`config.py` 파일에서 다양한 설정을 변경할 수 있습니다:

- `HASHTAGS`: 기본 해시태그 목록
- `LIKE_PER_HASHTAG`: 각 해시태그당 좋아요 수
- `COMMENTS`: 기본 댓글 목록 (AI 생성 실패 시 사용)
- `FOLLOW_RATIO`: 좋아요 누른 게시물 중 팔로우할 비율
- `POST_FREQUENCY`: 하루에 게시할 게시물 수
- `POST_TOPICS`: 게시물 주제 목록
- `USE_AI_COMMENTS`: AI로 댓글 생성 여부
- `USE_AI_CAPTIONS`: AI로 캡션 생성 여부
- `COMMENT_STYLE`: 댓글 스타일
- `CAPTION_STYLE`: 캡션 스타일

## 주의사항

- 인스타그램의 자동화 정책을 위반할 수 있으므로 적절한 시간 간격을 두고 사용하세요.
- 계정 제한이나 차단을 방지하기 위해 하루에 너무 많은 상호작용을 하지 마세요.
- 개인 계정에서만 사용하고 상업적 목적으로 사용하지 마세요.
- 이 프로그램은 교육 목적으로만 제공됩니다.

## 라이선스

이 프로젝트는 MIT 라이선스를 따릅니다.
