"""
Gemini API를 활용한 AI 기능 모듈
"""
import os
import re
import google.generativeai as genai
from PIL import Image
import config

# Gemini API 설정
genai.configure(api_key=config.GEMINI_API_KEY)

class GeminiHelper:
    """Gemini API를 활용한 AI 기능 클래스"""

    def __init__(self):
        """초기화 함수"""
        self.model = genai.GenerativeModel('gemini-2.0-flash')
        self.vision_model = genai.GenerativeModel('gemini-pro-vision')

    def _remove_emojis(self, text):
        """이모지 및 특수 유니코드 문자 제거"""
        # BMP(Basic Multilingual Plane) 외의 문자 제거
        return re.sub(r'[^\u0000-\uFFFF]', '', text)

    def generate_comment(self, post_description, post_hashtags=None, style=None):
        """
        게시물 설명을 기반으로 댓글 생성

        Args:
            post_description (str): 게시물 설명 또는 내용
            post_hashtags (list, optional): 게시물 해시태그 목록
            style (str, optional): 댓글 스타일 (기본값: config.COMMENT_STYLE)

        Returns:
            str: 생성된 댓글
        """
        if not style:
            style = config.COMMENT_STYLE

        hashtags_text = ""
        if post_hashtags:
            hashtags_text = f"해시태그: {', '.join(post_hashtags)}"

        prompt = f"""
        다음 인스타그램 게시물에 {style} 댓글을 작성해주세요:

        게시물 내용: {post_description}
        {hashtags_text}

        댓글은 다음 조건을 만족해야 합니다:
        1. 50자 이내로 짧게 작성
        2. 이모지는 사용하지 말고 텍스트로만 작성
        3. 자연스럽고 진정성 있게 작성
        4. 질문이나 대화를 유도하는 내용 포함
        5. 특수 유니코드 문자를 사용하지 말고 기본 문자만 사용

        댓글만 작성해주세요. 다른 설명은 필요 없습니다.
        """

        try:
            response = self.model.generate_content(prompt)
            comment = response.text.strip()
            # 이모지 및 특수 문자 제거
            comment = self._remove_emojis(comment)
            return comment
        except Exception as e:
            print(f"댓글 생성 중 오류 발생: {e}")
            # 오류 발생 시 기본 댓글 중 하나 반환
            import random
            return random.choice(config.COMMENTS)

    def analyze_image(self, image_path):
        """
        이미지 분석하여 설명 생성

        Args:
            image_path (str): 이미지 파일 경로

        Returns:
            str: 이미지 설명
        """
        try:
            image = Image.open(image_path)

            prompt = """
            이 이미지를 자세히 분석해주세요. 다음 정보를 포함해주세요:
            1. 이미지에 무엇이 보이는지
            2. 이미지의 분위기나 느낌
            3. 이미지의 주요 색상이나 스타일

            간결하게 3-4문장으로 설명해주세요.
            """

            response = self.vision_model.generate_content([prompt, image])
            return response.text.strip()
        except Exception as e:
            print(f"이미지 분석 중 오류 발생: {e}")
            return "이미지 분석에 실패했습니다."

    def generate_caption(self, image_path=None, topic=None, style=None):
        """
        이미지와 주제를 기반으로 인스타그램 캡션 생성

        Args:
            image_path (str, optional): 이미지 파일 경로
            topic (str, optional): 게시물 주제
            style (str, optional): 캡션 스타일 (기본값: config.CAPTION_STYLE)

        Returns:
            str: 생성된 캡션
        """
        if not style:
            style = config.CAPTION_STYLE

        image_description = ""
        if image_path and os.path.exists(image_path):
            image_description = self.analyze_image(image_path)

        topic_text = ""
        if topic:
            topic_text = f"주제: {topic}"

        prompt = f"""
        다음 정보를 바탕으로 {style} 인스타그램 캡션을 작성해주세요:

        {image_description}
        {topic_text}

        캡션은 다음 조건을 만족해야 합니다:
        1. 100-150자 내외로 작성
        2. 이모지는 사용하지 말고 텍스트로만 작성
        3. 관련 해시태그 5-7개 포함 (한글과 영어 혼합)
        4. 감성적이고 공감을 불러일으키는 내용
        5. 특수 유니코드 문자를 사용하지 말고 기본 문자만 사용

        캡션만 작성해주세요. 다른 설명은 필요 없습니다.
        """

        try:
            response = self.model.generate_content(prompt)
            caption = response.text.strip()
            # 이모지 및 특수 문자 제거
            caption = self._remove_emojis(caption)
            return caption
        except Exception as e:
            print(f"캡션 생성 중 오류 발생: {e}")
            return f"오늘의 {topic or '일상'} #일상 #데일리 #인스타그램"

    def generate_hashtags(self, content, count=10):
        """
        콘텐츠 내용을 기반으로 해시태그 생성

        Args:
            content (str): 콘텐츠 내용
            count (int, optional): 생성할 해시태그 수

        Returns:
            list: 생성된 해시태그 목록
        """
        prompt = f"""
        다음 콘텐츠에 적합한 인스타그램 해시태그를 {count}개 생성해주세요:

        콘텐츠: {content}

        해시태그는 다음 조건을 만족해야 합니다:
        1. 인기 있는 해시태그와 틈새 해시태그 혼합
        2. 한글과 영어 해시태그 혼합
        3. 콘텐츠와 관련성 높은 해시태그

        해시태그만 작성해주세요. # 기호는 제외하고 단어만 리스트로 반환해주세요.
        """

        try:
            response = self.model.generate_content(prompt)
            hashtags = response.text.strip().split('\n')
            # # 기호 제거 및 공백 제거
            hashtags = [tag.strip().replace('#', '') for tag in hashtags if tag.strip()]
            return hashtags[:count]  # 요청한 개수만큼 반환
        except Exception as e:
            print(f"해시태그 생성 중 오류 발생: {e}")
            return config.HASHTAGS
