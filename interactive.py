"""
인스타그램 자동화 대화형 모드
"""
import os
import sys
import time
import random
import logging
from datetime import datetime

import config
from instagram_bot import InstagramBot
from gemini_helper import GeminiHelper
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# 로깅 설정
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("instagram_interactive.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("InstagramInteractive")

class InteractiveMode:
    """인스타그램 자동화 대화형 모드 클래스"""

    def __init__(self):
        """초기화 함수"""
        self.bot = None
        self.gemini = None
        self.logged_in = False

    def initialize(self):
        """봇 초기화 및 로그인"""
        try:
            print("\n인스타그램 자동화 프로그램을 초기화하는 중...")
            self.bot = InstagramBot()
            self.gemini = GeminiHelper()

            # 로그인
            if self.bot.login():
                self.logged_in = True
                print("\n로그인 성공! 이제 인스타그램 자동화를 시작할 수 있습니다.")
                return True
            else:
                print("\n로그인에 실패했습니다. 환경 변수를 확인해주세요.")
                return False
        except Exception as e:
            print(f"\n초기화 중 오류 발생: {e}")
            return False

    def close(self):
        """봇 종료"""
        if self.bot:
            self.bot.close()
            print("\n프로그램이 종료되었습니다.")

    def show_menu(self):
        """메인 메뉴 표시"""
        print("\n" + "=" * 50)
        print("인스타그램 자동화 프로그램 - 대화형 모드")
        print("=" * 50)
        print("1. 해시태그 기반 자동 상호작용")
        print("2. 게시물 업로드 (AI 캡션 생성)")
        print("3. 게시물 업로드 (직접 캡션 작성)")
        print("4. 특정 게시물에 댓글 작성")
        print("5. 특정 사용자 팔로우")
        print("6. 팔로잉 관리")
        print("7. DM 보내기")
        print("8. 설정 변경")
        print("0. 종료")
        print("=" * 50)

    def hashtag_interaction(self):
        """해시태그 기반 상호작용"""
        print("\n" + "=" * 50)
        print("해시태그 기반 자동 상호작용")
        print("=" * 50)

        # 해시태그 입력
        hashtags_input = input("\n상호작용할 해시태그를 입력하세요 (쉼표로 구분): ")
        if not hashtags_input.strip():
            hashtags = config.HASHTAGS
            print(f"기본 해시태그를 사용합니다: {', '.join(hashtags)}")
        else:
            hashtags = [tag.strip() for tag in hashtags_input.split(',')]
            print(f"입력한 해시태그: {', '.join(hashtags)}")

        # 게시물 수 입력
        try:
            posts_input = input("\n각 해시태그당 상호작용할 게시물 수를 입력하세요 (기본값: 5): ")
            posts_per_hashtag = int(posts_input) if posts_input.strip() else 5
        except ValueError:
            posts_per_hashtag = 5
            print("유효하지 않은 입력입니다. 기본값 5를 사용합니다.")

        # 확인
        confirm = input(f"\n{len(hashtags)}개의 해시태그에 대해 각각 {posts_per_hashtag}개의 게시물과 상호작용합니다. 계속하시겠습니까? (y/n): ")
        if confirm.lower() != 'y':
            print("상호작용이 취소되었습니다.")
            return

        # 상호작용 실행
        print("\n상호작용을 시작합니다...")
        results = self.bot.run_hashtag_interaction(hashtags, posts_per_hashtag)

        # 결과 출력
        print("\n" + "=" * 50)
        print("상호작용 결과 요약:")
        print(f"총 상호작용: {results['total_interactions']}개")
        print(f"좋아요: {results['likes']}개")
        print(f"댓글: {results['comments']}개")
        print(f"팔로우: {results['follows']}개")
        print("=" * 50)

    def upload_post_with_ai(self):
        """AI 캡션으로 게시물 업로드"""
        print("\n" + "=" * 50)
        print("게시물 업로드 (AI 캡션 생성)")
        print("=" * 50)

        # 이미지 폴더 확인
        image_folder = input("\n이미지 폴더 경로를 입력하세요 (기본값: images): ")
        if not image_folder.strip():
            image_folder = "images"

        if not os.path.exists(image_folder):
            print(f"이미지 폴더가 존재하지 않습니다: {image_folder}")
            create_folder = input("폴더를 생성하시겠습니까? (y/n): ")
            if create_folder.lower() == 'y':
                os.makedirs(image_folder)
                print(f"폴더가 생성되었습니다: {image_folder}")
            else:
                return

        # 이미지 파일 찾기
        image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

        if not image_files:
            print(f"업로드할 이미지 파일이 없습니다: {image_folder}")
            return

        # 이미지 선택
        print("\n사용 가능한 이미지 파일:")
        for i, file in enumerate(image_files, 1):
            print(f"{i}. {file}")

        try:
            image_index = int(input("\n업로드할 이미지 번호를 선택하세요: ")) - 1
            if image_index < 0 or image_index >= len(image_files):
                print("유효하지 않은 번호입니다.")
                return

            image_file = image_files[image_index]
            image_path = os.path.join(image_folder, image_file)
        except ValueError:
            print("유효하지 않은 입력입니다.")
            return

        # 주제 선택
        topic = input("\n게시물 주제를 입력하세요 (예: 여행, 음식, 패션, 일상): ")
        if not topic.strip() and config.POST_TOPICS:
            topic = random.choice(config.POST_TOPICS)
            print(f"기본 주제를 사용합니다: {topic}")

        # AI 캡션 생성
        print("\nAI로 캡션을 생성하는 중...")
        caption = self.gemini.generate_caption(image_path, topic)

        print("\n생성된 캡션:")
        print("-" * 50)
        print(caption)
        print("-" * 50)

        # 캡션 수정 옵션
        edit_option = input("\n캡션을 수정하시겠습니까? (y/n): ")
        if edit_option.lower() == 'y':
            new_caption = input("\n새 캡션을 입력하세요:\n")
            if new_caption.strip():
                caption = new_caption
                print("캡션이 수정되었습니다.")

        # 확인
        confirm = input("\n이 게시물을 업로드하시겠습니까? (y/n): ")
        if confirm.lower() != 'y':
            print("업로드가 취소되었습니다.")
            return

        # 게시물 업로드
        print("\n게시물을 업로드하는 중...")
        success = self.bot.upload_post(image_path, caption=caption)

        if success:
            print("\n게시물 업로드 성공!")
        else:
            print("\n게시물 업로드 실패!")

    def upload_post_with_custom_caption(self):
        """직접 작성한 캡션으로 게시물 업로드"""
        print("\n" + "=" * 50)
        print("게시물 업로드 (직접 캡션 작성)")
        print("=" * 50)

        # 이미지 폴더 확인
        image_folder = input("\n이미지 폴더 경로를 입력하세요 (기본값: images): ")
        if not image_folder.strip():
            image_folder = "images"

        if not os.path.exists(image_folder):
            print(f"이미지 폴더가 존재하지 않습니다: {image_folder}")
            create_folder = input("폴더를 생성하시겠습니까? (y/n): ")
            if create_folder.lower() == 'y':
                os.makedirs(image_folder)
                print(f"폴더가 생성되었습니다: {image_folder}")
            else:
                return

        # 이미지 파일 찾기
        image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

        if not image_files:
            print(f"업로드할 이미지 파일이 없습니다: {image_folder}")
            return

        # 이미지 선택
        print("\n사용 가능한 이미지 파일:")
        for i, file in enumerate(image_files, 1):
            print(f"{i}. {file}")

        try:
            image_index = int(input("\n업로드할 이미지 번호를 선택하세요: ")) - 1
            if image_index < 0 or image_index >= len(image_files):
                print("유효하지 않은 번호입니다.")
                return

            image_file = image_files[image_index]
            image_path = os.path.join(image_folder, image_file)
        except ValueError:
            print("유효하지 않은 입력입니다.")
            return

        # 캡션 직접 작성
        print("\n캡션을 직접 작성해주세요:")
        caption = input("\n")

        if not caption.strip():
            print("캡션이 비어있습니다. 기본 캡션을 사용합니다.")
            caption = f"#인스타그램 #일상 #데일리"

        # 해시태그 추가 옵션
        add_hashtags = input("\n해시태그를 자동으로 추가하시겠습니까? (y/n): ")
        if add_hashtags.lower() == 'y':
            # 이미지 분석으로 해시태그 추천
            print("\n이미지를 분석하여 해시태그를 추천하는 중...")
            try:
                image_description = self.gemini.analyze_image(image_path)
                hashtags = self.gemini.generate_hashtags(image_description, count=5)

                print("\n추천 해시태그:")
                for tag in hashtags:
                    print(f"#{tag}")

                use_hashtags = input("\n이 해시태그를 사용하시겠습니까? (y/n): ")
                if use_hashtags.lower() == 'y':
                    hashtag_text = " ".join([f"#{tag}" for tag in hashtags])
                    caption = f"{caption}\n\n{hashtag_text}"
                    print("해시태그가 추가되었습니다.")
            except Exception as e:
                print(f"해시태그 생성 중 오류 발생: {e}")

        # 확인
        print("\n최종 캡션:")
        print("-" * 50)
        print(caption)
        print("-" * 50)

        confirm = input("\n이 게시물을 업로드하시겠습니까? (y/n): ")
        if confirm.lower() != 'y':
            print("업로드가 취소되었습니다.")
            return

        # 게시물 업로드
        print("\n게시물을 업로드하는 중...")
        success = self.bot.upload_post(image_path, caption=caption)

        if success:
            print("\n게시물 업로드 성공!")
        else:
            print("\n게시물 업로드 실패!")

    def comment_on_post(self):
        """특정 게시물에 댓글 작성"""
        print("\n" + "=" * 50)
        print("특정 게시물에 댓글 작성")
        print("=" * 50)

        # 게시물 URL 입력
        post_url = input("\n댓글을 작성할 게시물 URL을 입력하세요: ")
        if not post_url.strip() or "instagram.com/p/" not in post_url:
            print("유효하지 않은 인스타그램 게시물 URL입니다.")
            return

        # 댓글 작성 방식 선택
        comment_option = input("\n댓글 작성 방식을 선택하세요 (1: AI 생성, 2: 직접 작성): ")

        if comment_option == "1":
            # AI로 댓글 생성
            print("\n게시물을 분석하여 AI로 댓글을 생성하는 중...")

            # 게시물 페이지 열기
            self.bot.driver.get(post_url)
            time.sleep(3)  # 페이지 로딩 대기

            try:
                # 게시물 내용 가져오기
                post_text = self.bot.driver.find_element(By.XPATH, "//div[contains(@class, '_a9zs')]//span").text

                # 해시태그 가져오기
                hashtags = []
                try:
                    hashtag_elements = self.bot.driver.find_elements(By.XPATH, "//a[contains(@href, '/explore/tags/')]")
                    for element in hashtag_elements:
                        tag = element.text.replace('#', '')
                        if tag:
                            hashtags.append(tag)
                except:
                    pass

                # 댓글 스타일 입력
                style = input("\n댓글 스타일을 입력하세요 (예: 친근한, 전문적인, 유머러스한): ")
                if not style.strip():
                    style = config.COMMENT_STYLE

                # AI로 댓글 생성
                comment = self.gemini.generate_comment(post_text, hashtags, style)

                print("\n생성된 댓글:")
                print("-" * 50)
                print(comment)
                print("-" * 50)

                # 댓글 수정 옵션
                edit_option = input("\n댓글을 수정하시겠습니까? (y/n): ")
                if edit_option.lower() == 'y':
                    new_comment = input("\n새 댓글을 입력하세요: ")
                    if new_comment.strip():
                        comment = new_comment
                        print("댓글이 수정되었습니다.")

            except Exception as e:
                print(f"\n게시물 분석 중 오류 발생: {e}")
                comment = input("\n댓글을 직접 입력하세요: ")

        else:
            # 직접 댓글 작성
            comment = input("\n댓글을 직접 입력하세요: ")

        if not comment.strip():
            print("댓글이 비어있습니다. 작업이 취소되었습니다.")
            return

        # 확인
        confirm = input(f"\n다음 댓글을 게시하시겠습니까? '{comment}' (y/n): ")
        if confirm.lower() != 'y':
            print("댓글 작성이 취소되었습니다.")
            return

        # 댓글 작성
        print("\n댓글을 작성하는 중...")

        # 게시물 페이지 열기 (다시 한번)
        self.bot.driver.get(post_url)
        time.sleep(3)  # 페이지 로딩 대기

        # 댓글 작성 시도
        try:
            # 이모지 및 특수 문자 제거
            safe_comment = self.gemini._remove_emojis(comment)
            if safe_comment != comment:
                print("\n이모지 또는 특수 문자가 제거되었습니다.")
                comment = safe_comment
                print(f"수정된 댓글: {comment}")

            # 댓글 입력창 찾기
            comment_input = WebDriverWait(self.bot.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//textarea[contains(@placeholder, '댓글') or contains(@placeholder, 'comment')]"))
            )

            # 댓글 입력창 클릭
            comment_input.click()
            print("댓글 입력창 클릭 성공")
            time.sleep(1)

            # 댓글 입력
            comment_input = WebDriverWait(self.bot.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//textarea[contains(@placeholder, '댓글') or contains(@placeholder, 'comment')]"))
            )
            comment_input.clear()

            # 한 글자씩 입력 (더 안정적인 방법)
            print("댓글 입력 중...")
            for char in comment:
                comment_input.send_keys(char)
                time.sleep(0.01)  # 약간의 지연

            print("댓글 입력 완료")
            time.sleep(1)

            # 게시 버튼 클릭
            post_button = WebDriverWait(self.bot.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '게시') or contains(text(), 'Post')]"))
            )
            post_button.click()
            print("게시 버튼 클릭 성공")
            time.sleep(2)

            # 댓글 게시 확인
            try:
                # 댓글 입력창이 비워졌는지 확인
                empty_input = WebDriverWait(self.bot.driver, 5).until(
                    EC.text_to_be_present_in_element_value((By.XPATH, "//textarea[contains(@placeholder, '댓글') or contains(@placeholder, 'comment')]"), "")
                )
                if empty_input:
                    print("\n댓글 작성 성공! 댓글 입력창이 비워졌습니다.")
                    return
            except:
                pass

            print("\n댓글이 작성되었을 수 있지만 확인할 수 없습니다.")

        except Exception as e:
            print(f"\n댓글 작성 중 오류 발생: {e}")
            print("\n댓글 작성 실패!")

    def follow_user(self):
        """특정 사용자 팔로우"""
        print("\n" + "=" * 50)
        print("특정 사용자 팔로우")
        print("=" * 50)

        # 사용자 이름 또는 URL 입력
        user_input = input("\n팔로우할 사용자 이름 또는 프로필 URL을 입력하세요: ")

        if not user_input.strip():
            print("입력이 비어있습니다.")
            return

        # URL 형식 확인 및 변환
        if "instagram.com/" in user_input:
            # URL에서 사용자 이름 추출
            username = user_input.split("instagram.com/")[-1].split("/")[0].split("?")[0]
        else:
            username = user_input.strip()

        # 확인
        confirm = input(f"\n사용자 '{username}'을(를) 팔로우하시겠습니까? (y/n): ")
        if confirm.lower() != 'y':
            print("팔로우가 취소되었습니다.")
            return

        print(f"\n사용자 '{username}' 팔로우 중...")
        success = self.bot.follow_user(username)

        if success:
            print(f"\n사용자 '{username}' 팔로우 성공!")
        else:
            print(f"\n사용자 '{username}' 팔로우 실패!")

            # 세션 오류가 발생했을 수 있으므로 재시도 옵션 제공
            retry = input("\n다시 시도하시겠습니까? (y/n): ")
            if retry.lower() == 'y':
                print("\n다시 시도 중...")

                # 브라우저 재시작
                try:
                    self.bot.driver.quit()

                    # 새 브라우저 세션 시작

                    chrome_options = Options()
                    chrome_options.add_argument("--no-sandbox")
                    chrome_options.add_argument("--disable-dev-shm-usage")
                    chrome_options.add_argument("--disable-notifications")
                    chrome_options.add_argument("--disable-popup-blocking")
                    chrome_options.add_argument("--start-maximized")
                    chrome_options.add_argument("--disable-infobars")
                    chrome_options.add_argument("--disable-extensions")
                    chrome_options.add_argument("--disable-gpu")
                    chrome_options.add_argument("--lang=ko-KR")
                    chrome_options.add_argument("--log-level=3")  # 로그 레벨 최소화

                    service = Service(ChromeDriverManager().install())
                    self.bot.driver = webdriver.Chrome(service=service, options=chrome_options)
                    self.bot.driver.implicitly_wait(config.IMPLICIT_WAIT)

                    # 다시 로그인
                    self.bot.logged_in = False
                    if self.bot.login():
                        # 다시 팔로우 시도
                        success = self.bot.follow_user(username)

                        if success:
                            print(f"\n사용자 '{username}' 팔로우 성공!")
                        else:
                            print(f"\n사용자 '{username}' 팔로우 실패!")
                except Exception as e:
                    print(f"\n브라우저 재시작 중 오류 발생: {e}")

    def change_settings(self):
        """설정 변경"""
        print("\n" + "=" * 50)
        print("설정 변경")
        print("=" * 50)
        print("1. AI 댓글 생성 설정")
        print("2. AI 캡션 생성 설정")
        print("3. 댓글 스타일 변경")
        print("4. 캡션 스타일 변경")
        print("0. 이전 메뉴로 돌아가기")
        print("=" * 50)

        option = input("\n옵션을 선택하세요: ")

        if option == "1":
            current = "사용" if config.USE_AI_COMMENTS else "사용 안 함"
            print(f"\n현재 AI 댓글 생성 설정: {current}")

            new_setting = input("\nAI 댓글 생성을 사용하시겠습니까? (y/n): ")
            config.USE_AI_COMMENTS = new_setting.lower() == 'y'

            print(f"\nAI 댓글 생성 설정이 변경되었습니다: {'사용' if config.USE_AI_COMMENTS else '사용 안 함'}")

        elif option == "2":
            current = "사용" if config.USE_AI_CAPTIONS else "사용 안 함"
            print(f"\n현재 AI 캡션 생성 설정: {current}")

            new_setting = input("\nAI 캡션 생성을 사용하시겠습니까? (y/n): ")
            config.USE_AI_CAPTIONS = new_setting.lower() == 'y'

            print(f"\nAI 캡션 생성 설정이 변경되었습니다: {'사용' if config.USE_AI_CAPTIONS else '사용 안 함'}")

        elif option == "3":
            print(f"\n현재 댓글 스타일: {config.COMMENT_STYLE}")

            new_style = input("\n새 댓글 스타일을 입력하세요: ")
            if new_style.strip():
                config.COMMENT_STYLE = new_style
                print(f"\n댓글 스타일이 변경되었습니다: {config.COMMENT_STYLE}")
            else:
                print("\n입력이 비어있어 변경되지 않았습니다.")

        elif option == "4":
            print(f"\n현재 캡션 스타일: {config.CAPTION_STYLE}")

            new_style = input("\n새 캡션 스타일을 입력하세요: ")
            if new_style.strip():
                config.CAPTION_STYLE = new_style
                print(f"\n캡션 스타일이 변경되었습니다: {config.CAPTION_STYLE}")
            else:
                print("\n입력이 비어있어 변경되지 않았습니다.")

    def manage_following(self):
        """팔로잉 관리"""
        print("\n" + "=" * 50)
        print("팔로잉 관리")
        print("=" * 50)
        print("1. 팔로잉 목록 보기")
        print("2. 팔로워 목록 보기")
        print("3. 맞팔로우하지 않는 사용자 찾기")
        print("4. 특정 사용자 언팔로우")
        print("5. 맞팔로우하지 않는 사용자 자동 언팔로우")
        print("0. 이전 메뉴로 돌아가기")
        print("=" * 50)

        choice = input("\n원하는 작업을 선택하세요: ")

        if choice == "1":
            self._view_following_list()
        elif choice == "2":
            self._view_followers_list()
        elif choice == "3":
            self._find_non_followers()
        elif choice == "4":
            self._unfollow_specific_user()
        elif choice == "5":
            self._unfollow_non_followers()
        elif choice == "0":
            return
        else:
            print("\n유효하지 않은 선택입니다. 다시 시도해주세요.")

    def _view_following_list(self):
        """팔로잉 목록 보기"""
        print("\n팔로잉 목록을 가져오는 중...")

        try:
            count_input = input("\n가져올 팔로잉 수를 입력하세요 (기본값: 50): ")
            max_count = int(count_input) if count_input.strip() else 50
        except ValueError:
            max_count = 50
            print("유효하지 않은 입력입니다. 기본값 50을 사용합니다.")

        following_list = self.bot.get_following_list(max_count)

        if following_list:
            print("\n팔로잉 목록:")
            for i, username in enumerate(following_list, 1):
                print(f"{i}. {username}")

            print(f"\n총 {len(following_list)}명의 팔로잉을 표시했습니다.")

            # 파일로 저장 옵션
            save_option = input("\n이 목록을 파일로 저장하시겠습니까? (y/n): ")
            if save_option.lower() == 'y':
                filename = input("\n저장할 파일 이름을 입력하세요 (기본값: following_list.txt): ")
                if not filename.strip():
                    filename = "following_list.txt"

                with open(filename, 'w', encoding='utf-8') as f:
                    for username in following_list:
                        f.write(f"{username}\n")

                print(f"\n팔로잉 목록이 {filename} 파일에 저장되었습니다.")
        else:
            print("\n팔로잉 목록을 가져오지 못했습니다.")

    def _view_followers_list(self):
        """팔로워 목록 보기"""
        print("\n팔로워 목록을 가져오는 중...")

        try:
            count_input = input("\n가져올 팔로워 수를 입력하세요 (기본값: 50): ")
            max_count = int(count_input) if count_input.strip() else 50
        except ValueError:
            max_count = 50
            print("유효하지 않은 입력입니다. 기본값 50을 사용합니다.")

        followers_list = self.bot.get_followers_list(max_count)

        if followers_list:
            print("\n팔로워 목록:")
            for i, username in enumerate(followers_list, 1):
                print(f"{i}. {username}")

            print(f"\n총 {len(followers_list)}명의 팔로워를 표시했습니다.")

            # 파일로 저장 옵션
            save_option = input("\n이 목록을 파일로 저장하시겠습니까? (y/n): ")
            if save_option.lower() == 'y':
                filename = input("\n저장할 파일 이름을 입력하세요 (기본값: followers_list.txt): ")
                if not filename.strip():
                    filename = "followers_list.txt"

                with open(filename, 'w', encoding='utf-8') as f:
                    for username in followers_list:
                        f.write(f"{username}\n")

                print(f"\n팔로워 목록이 {filename} 파일에 저장되었습니다.")
        else:
            print("\n팔로워 목록을 가져오지 못했습니다.")

    def _find_non_followers(self):
        """맞팔로우하지 않는 사용자 찾기"""
        print("\n맞팔로우하지 않는 사용자를 찾는 중...")

        try:
            count_input = input("\n검사할 사용자 수를 입력하세요 (기본값: 50): ")
            max_count = int(count_input) if count_input.strip() else 50
        except ValueError:
            max_count = 50
            print("유효하지 않은 입력입니다. 기본값 50을 사용합니다.")

        non_followers = self.bot.find_non_followers(max_count)

        if non_followers:
            print("\n맞팔로우하지 않는 사용자 목록:")
            for i, username in enumerate(non_followers, 1):
                print(f"{i}. {username}")

            print(f"\n총 {len(non_followers)}명이 맞팔로우하지 않고 있습니다.")

            # 파일로 저장 옵션
            save_option = input("\n이 목록을 파일로 저장하시겠습니까? (y/n): ")
            if save_option.lower() == 'y':
                filename = input("\n저장할 파일 이름을 입력하세요 (기본값: non_followers.txt): ")
                if not filename.strip():
                    filename = "non_followers.txt"

                with open(filename, 'w', encoding='utf-8') as f:
                    for username in non_followers:
                        f.write(f"{username}\n")

                print(f"\n맞팔로우하지 않는 사용자 목록이 {filename} 파일에 저장되었습니다.")
        else:
            print("\n맞팔로우하지 않는 사용자를 찾지 못했습니다.")

    def _unfollow_specific_user(self):
        """특정 사용자 언팔로우"""
        print("\n" + "=" * 50)
        print("특정 사용자 언팔로우")
        print("=" * 50)

        username = input("\n언팔로우할 사용자 이름을 입력하세요: ")

        if not username.strip():
            print("\n사용자 이름이 비어있습니다.")
            return

        confirm = input(f"\n정말 '{username}'을(를) 언팔로우하시겠습니까? (y/n): ")
        if confirm.lower() != 'y':
            print("\n언팔로우가 취소되었습니다.")
            return

        success = self.bot.unfollow_user(username)

        if success:
            print(f"\n'{username}'을(를) 성공적으로 언팔로우했습니다.")
        else:
            print(f"\n'{username}'을(를) 언팔로우하지 못했습니다.")

    def _unfollow_non_followers(self):
        """맞팔로우하지 않는 사용자 자동 언팔로우"""
        print("\n맞팔로우하지 않는 사용자를 자동으로 언팔로우합니다.")

        try:
            count_input = input("\n검사할 사용자 수를 입력하세요 (기본값: 50): ")
            max_count = int(count_input) if count_input.strip() else 50
        except ValueError:
            max_count = 50
            print("유효하지 않은 입력입니다. 기본값 50을 사용합니다.")

        non_followers = self.bot.find_non_followers(max_count)

        if not non_followers:
            print("\n맞팔로우하지 않는 사용자를 찾지 못했습니다.")
            return

        print(f"\n총 {len(non_followers)}명이 맞팔로우하지 않고 있습니다:")
        for i, username in enumerate(non_followers, 1):
            print(f"{i}. {username}")

        try:
            unfollow_count_input = input(f"\n언팔로우할 사용자 수를 입력하세요 (최대 {len(non_followers)}): ")
            unfollow_count = int(unfollow_count_input) if unfollow_count_input.strip() else len(non_followers)
            unfollow_count = min(unfollow_count, len(non_followers))
        except ValueError:
            unfollow_count = len(non_followers)
            print(f"유효하지 않은 입력입니다. 기본값 {unfollow_count}을 사용합니다.")

        confirm = input(f"\n정말 {unfollow_count}명의 사용자를 언팔로우하시겠습니까? (y/n): ")
        if confirm.lower() != 'y':
            print("\n언팔로우가 취소되었습니다.")
            return

        success_count = 0
        for i, username in enumerate(non_followers[:unfollow_count], 1):
            print(f"\n{i}/{unfollow_count} - '{username}'을(를) 언팔로우하는 중...")
            if self.bot.unfollow_user(username):
                success_count += 1
                print(f"'{username}'을(를) 성공적으로 언팔로우했습니다.")
            else:
                print(f"'{username}'을(를) 언팔로우하지 못했습니다.")

            # 자연스러운 지연
            if i < unfollow_count:
                delay = random.uniform(2, 5)
                print(f"다음 사용자까지 {delay:.1f}초 대기 중...")
                time.sleep(delay)

        print(f"\n총 {success_count}/{unfollow_count}명의 사용자를 언팔로우했습니다.")

    def send_dm_menu(self):
        """DM 보내기 메뉴"""
        print("\n" + "=" * 50)
        print("DM 보내기")
        print("=" * 50)
        print("1. 개인 DM 보내기")
        print("2. 그룹 DM 보내기")
        print("3. AI로 메시지 생성하여 DM 보내기")
        print("0. 이전 메뉴로 돌아가기")
        print("=" * 50)

        choice = input("\n원하는 작업을 선택하세요: ")

        if choice == "1":
            self._send_personal_dm()
        elif choice == "2":
            self._send_group_dm()
        elif choice == "3":
            self._send_ai_dm()
        elif choice == "0":
            return
        else:
            print("\n유효하지 않은 선택입니다. 다시 시도해주세요.")

    def _send_personal_dm(self):
        """개인 DM 보내기"""
        print("\n" + "=" * 50)
        print("개인 DM 보내기")
        print("=" * 50)

        # 사용자 이름 입력
        username = input("\nDM을 보낼 사용자 이름을 입력하세요: ")
        if not username.strip():
            print("사용자 이름이 비어있습니다.")
            return

        # 메시지 입력
        message = input("\n보낼 메시지를 입력하세요: ")
        if not message.strip():
            print("메시지가 비어있습니다.")
            return

        # 확인
        confirm = input(f"\n사용자 '{username}'에게 다음 메시지를 보내시겠습니까?\n\n{message}\n\n(y/n): ")
        if confirm.lower() != 'y':
            print("\nDM 전송이 취소되었습니다.")
            return

        # DM 전송
        print(f"\n사용자 '{username}'에게 DM 전송 중...")
        success = self.bot.send_dm(username, message)

        if success:
            print(f"\n사용자 '{username}'에게 DM 전송 성공!")
        else:
            print(f"\n사용자 '{username}'에게 DM 전송 실패!")

    def _send_group_dm(self):
        """그룹 DM 보내기"""
        print("\n" + "=" * 50)
        print("그룹 DM 보내기")
        print("=" * 50)

        # 사용자 이름 목록 입력
        usernames_input = input("\nDM을 보낼 사용자 이름을 쉼표로 구분하여 입력하세요: ")
        if not usernames_input.strip():
            print("사용자 이름이 비어있습니다.")
            return

        usernames = [username.strip() for username in usernames_input.split(',') if username.strip()]
        if not usernames:
            print("유효한 사용자 이름이 없습니다.")
            return

        print(f"\n선택된 사용자 ({len(usernames)}명):")
        for i, username in enumerate(usernames, 1):
            print(f"{i}. {username}")

        # 메시지 입력
        message = input("\n보낼 메시지를 입력하세요: ")
        if not message.strip():
            print("메시지가 비어있습니다.")
            return

        # 확인
        confirm = input(f"\n{len(usernames)}명의 사용자에게 다음 메시지를 보내시겠습니까?\n\n{message}\n\n(y/n): ")
        if confirm.lower() != 'y':
            print("\nDM 전송이 취소되었습니다.")
            return

        # DM 전송
        print(f"\n{len(usernames)}명의 사용자에게 그룹 DM 전송 중...")
        success = self.bot.send_group_dm(usernames, message)

        if success:
            print(f"\n{len(usernames)}명의 사용자에게 그룹 DM 전송 성공!")
        else:
            print(f"\n그룹 DM 전송 실패!")

    def _send_ai_dm(self):
        """AI로 메시지 생성하여 DM 보내기"""
        print("\n" + "=" * 50)
        print("AI로 메시지 생성하여 DM 보내기")
        print("=" * 50)

        # 사용자 이름 입력
        username = input("\nDM을 보낼 사용자 이름을 입력하세요: ")
        if not username.strip():
            print("사용자 이름이 비어있습니다.")
            return

        # 메시지 주제 입력
        topic = input("\n메시지 주제를 입력하세요 (예: 인사, 협업 제안, 질문 등): ")
        if not topic.strip():
            print("주제가 비어있습니다.")
            return

        # 메시지 스타일 입력
        style = input("\n메시지 스타일을 입력하세요 (예: 친근한, 전문적인, 격식있는 등): ")
        if not style.strip():
            style = "친근한"
            print(f"기본 스타일 '{style}'을 사용합니다.")

        # AI로 메시지 생성
        print("\nAI로 메시지를 생성하는 중...")

        prompt = f"""
        인스타그램 DM 메시지를 작성해주세요:

        주제: {topic}
        스타일: {style}

        메시지는 다음 조건을 만족해야 합니다:
        1. 100자 내외로 짧게 작성
        2. 이모지는 사용하지 말고 텍스트로만 작성
        3. 자연스럽고 진정성 있게 작성
        4. 특수 유니코드 문자를 사용하지 말고 기본 문자만 사용

        메시지만 작성해주세요. 다른 설명은 필요 없습니다.
        """

        try:
            response = self.gemini.model.generate_content(prompt)
            message = response.text.strip()

            # 이모지 및 특수 문자 제거
            message = self.gemini._remove_emojis(message)

            print("\n생성된 메시지:")
            print("-" * 50)
            print(message)
            print("-" * 50)

            # 메시지 수정 옵션
            edit_option = input("\n메시지를 수정하시겠습니까? (y/n): ")
            if edit_option.lower() == 'y':
                new_message = input("\n새 메시지를 입력하세요: ")
                if new_message.strip():
                    message = new_message
                    print("메시지가 수정되었습니다.")

            # 확인
            confirm = input(f"\n사용자 '{username}'에게 다음 메시지를 보내시겠습니까?\n\n{message}\n\n(y/n): ")
            if confirm.lower() != 'y':
                print("\nDM 전송이 취소되었습니다.")
                return

            # DM 전송
            print(f"\n사용자 '{username}'에게 DM 전송 중...")
            success = self.bot.send_dm(username, message)

            if success:
                print(f"\n사용자 '{username}'에게 DM 전송 성공!")
            else:
                print(f"\n사용자 '{username}'에게 DM 전송 실패!")

        except Exception as e:
            print(f"\nAI 메시지 생성 중 오류 발생: {e}")
            print("직접 메시지를 입력해주세요.")
            self._send_personal_dm()

    def run(self):
        """대화형 모드 실행"""
        if not self.initialize():
            return

        while True:
            self.show_menu()
            choice = input("\n원하는 작업을 선택하세요: ")

            if choice == "0":
                break
            elif choice == "1":
                self.hashtag_interaction()
            elif choice == "2":
                self.upload_post_with_ai()
            elif choice == "3":
                self.upload_post_with_custom_caption()
            elif choice == "4":
                self.comment_on_post()
            elif choice == "5":
                self.follow_user()
            elif choice == "6":
                self.manage_following()
            elif choice == "7":
                self.send_dm_menu()
            elif choice == "8":
                self.change_settings()
            else:
                print("\n유효하지 않은 선택입니다. 다시 시도해주세요.")

        self.close()

def main():
    """메인 함수"""
    try:
        interactive = InteractiveMode()
        interactive.run()
    except KeyboardInterrupt:
        print("\n\n사용자에 의해 프로그램이 중단되었습니다.")
        try:
            interactive.close()
        except:
            pass
    except Exception as e:
        print(f"\n\n프로그램 실행 중 오류 발생: {e}")
        try:
            interactive.close()
        except:
            pass

if __name__ == "__main__":
    main()
