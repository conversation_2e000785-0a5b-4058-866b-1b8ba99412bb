@echo off
echo 인스타그램 자동화 프로그램 설치를 시작합니다...
echo.

REM Python이 설치되어 있는지 확인
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python이 설치되어 있지 않습니다. Python을 먼저 설치해주세요.
    echo https://www.python.org/downloads/
    pause
    exit /b
)

REM 가상환경 생성
echo 가상환경을 생성하는 중...
python -m venv venv
if %errorlevel% neq 0 (
    echo 가상환경 생성에 실패했습니다.
    pause
    exit /b
)

REM 가상환경 활성화 및 패키지 설치
echo 가상환경을 활성화하고 필요한 패키지를 설치하는 중...
call venv\Scripts\activate.bat
python -m pip install --upgrade pip
pip install selenium==4.15.2 webdriver-manager==4.0.1 python-dotenv==1.0.0 google-generativeai==0.3.1 pillow==10.1.0 requests==2.31.0

REM .env 파일 생성 (이미 있는 경우 덮어쓰지 않음)
if not exist .env (
    echo # 인스타그램 계정 정보> .env
    echo INSTAGRAM_USERNAME=your_instagram_username>> .env
    echo INSTAGRAM_PASSWORD=your_instagram_password>> .env
    echo.>> .env
    echo # Gemini API 키>> .env
    echo GEMINI_API_KEY=your_gemini_api_key>> .env
    echo .env 파일이 생성되었습니다. 계정 정보와 API 키를 입력해주세요.
) else (
    echo .env 파일이 이미 존재합니다.
)

REM images 폴더 생성 (이미 있는 경우 생성하지 않음)
if not exist images (
    mkdir images
    echo images 폴더가 생성되었습니다.
) else (
    echo images 폴더가 이미 존재합니다.
)

echo.
echo 설치가 완료되었습니다!
echo.
echo 다음 단계:
echo 1. .env 파일을 열어 인스타그램 계정 정보와 Gemini API 키를 입력하세요.
echo 2. 'run.bat' 파일을 실행하여 프로그램을 시작하세요.
echo.
pause
