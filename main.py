"""
인스타그램 자동화 메인 프로그램
"""
import os
import sys
import time
import random
import argparse
import logging
from datetime import datetime

import config
from instagram_bot import InstagramBot
from gemini_helper import GeminiHelper
from interactive import InteractiveMode

# 로깅 설정
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("instagram_automation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("InstagramAutomation")

def check_environment():
    """환경 변수 확인"""
    missing_vars = []

    if not config.USERNAME:
        missing_vars.append("INSTAGRAM_USERNAME")

    if not config.PASSWORD:
        missing_vars.append("INSTAGRAM_PASSWORD")

    if not config.GEMINI_API_KEY:
        missing_vars.append("GEMINI_API_KEY")

    if missing_vars:
        logger.error(f"다음 환경 변수가 설정되지 않았습니다: {', '.join(missing_vars)}")
        logger.error(".env 파일을 생성하고 필요한 환경 변수를 설정해주세요.")
        logger.error(".env.example 파일을 참고하세요.")
        return False

    return True

def interact_with_hashtags(bot, hashtags=None, posts_per_hashtag=None):
    """해시태그 기반 상호작용"""
    logger.info("해시태그 기반 상호작용 시작...")
    results = bot.run_hashtag_interaction(hashtags, posts_per_hashtag)

    logger.info("=" * 50)
    logger.info("상호작용 결과 요약:")
    logger.info(f"총 상호작용: {results['total_interactions']}개")
    logger.info(f"좋아요: {results['likes']}개")
    logger.info(f"댓글: {results['comments']}개")
    logger.info(f"팔로우: {results['follows']}개")
    logger.info("=" * 50)

    return results

def upload_new_post(bot, image_folder="images", topic=None):
    """새 게시물 업로드"""
    logger.info("새 게시물 업로드 시작...")

    # 이미지 폴더 확인
    if not os.path.exists(image_folder):
        logger.error(f"이미지 폴더가 존재하지 않습니다: {image_folder}")
        return False

    # 이미지 파일 찾기
    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

    if not image_files:
        logger.error(f"업로드할 이미지 파일이 없습니다: {image_folder}")
        return False

    # 랜덤 이미지 선택
    image_file = random.choice(image_files)
    image_path = os.path.join(image_folder, image_file)

    # 주제 선택 (없는 경우)
    if not topic and config.POST_TOPICS:
        topic = random.choice(config.POST_TOPICS)

    # 게시물 업로드
    success = bot.upload_post(image_path, topic=topic)

    if success:
        logger.info(f"게시물 업로드 성공: {image_file}")

        # 업로드 후 이미지 파일 이동 또는 삭제 (선택 사항)
        # uploaded_folder = os.path.join(image_folder, "uploaded")
        # os.makedirs(uploaded_folder, exist_ok=True)
        # os.rename(image_path, os.path.join(uploaded_folder, image_file))

        return True
    else:
        logger.error(f"게시물 업로드 실패: {image_file}")
        return False

def parse_arguments():
    """명령줄 인수 파싱"""
    parser = argparse.ArgumentParser(description="인스타그램 자동화 프로그램")

    parser.add_argument("--mode", choices=["interact", "upload", "both", "interactive"], default="interact",
                        help="실행 모드 (interact: 상호작용, upload: 게시물 업로드, both: 둘 다, interactive: 대화형 모드)")

    parser.add_argument("--hashtags", nargs="+", default=None,
                        help="상호작용할 해시태그 목록 (공백으로 구분)")

    parser.add_argument("--posts", type=int, default=None,
                        help="각 해시태그당 상호작용할 게시물 수")

    parser.add_argument("--image-folder", default="images",
                        help="업로드할 이미지가 있는 폴더 경로")

    parser.add_argument("--topic", default=None,
                        help="게시물 주제")

    parser.add_argument("--caption", default=None,
                        help="게시물 캡션 (직접 지정)")

    parser.add_argument("--image", default=None,
                        help="업로드할 특정 이미지 파일 경로")

    return parser.parse_args()

def main():
    """메인 함수"""
    # 환경 변수 확인
    if not check_environment():
        sys.exit(1)

    # 명령줄 인수 파싱
    args = parse_arguments()

    # 대화형 모드 실행
    if args.mode == "interactive":
        try:
            interactive = InteractiveMode()
            interactive.run()
            return
        except Exception as e:
            logger.error(f"대화형 모드 실행 중 오류 발생: {e}")
            sys.exit(1)

    try:
        # 인스타그램 봇 초기화
        bot = InstagramBot()

        # 로그인
        if not bot.login():
            logger.error("로그인에 실패했습니다. 프로그램을 종료합니다.")
            bot.close()
            sys.exit(1)

        # 모드에 따라 실행
        if args.mode in ["interact", "both"]:
            interact_with_hashtags(bot, args.hashtags, args.posts)

        if args.mode in ["upload", "both"]:
            # 특정 이미지 파일이 지정된 경우
            if args.image:
                if os.path.exists(args.image):
                    logger.info(f"지정된 이미지 파일로 게시물 업로드: {args.image}")
                    bot.upload_post(args.image, caption=args.caption, topic=args.topic)
                else:
                    logger.error(f"지정된 이미지 파일이 존재하지 않습니다: {args.image}")
            else:
                upload_new_post(bot, args.image_folder, args.topic)

        # 브라우저 종료
        bot.close()
        logger.info("프로그램이 성공적으로 완료되었습니다.")

    except KeyboardInterrupt:
        logger.info("사용자에 의해 프로그램이 중단되었습니다.")
        try:
            bot.close()
        except:
            pass
        sys.exit(0)

    except Exception as e:
        logger.error(f"프로그램 실행 중 오류 발생: {e}")
        try:
            bot.close()
        except:
            pass
        sys.exit(1)

if __name__ == "__main__":
    main()
